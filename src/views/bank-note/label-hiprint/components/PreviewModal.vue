<template>
  <el-dialog
    v-model="visible"
    title=""
    :width="dialogWidth"
    :close-on-click-modal="false"
    :style="dialogStyle"
    class="preview-dialog"
    destroy-on-close
  >
    <template #header>
      <div class="preview-header">
        <div class="preview-title">
          <span>{{ title || '打印预览' }}</span>
          <el-tag v-if="templateInfo" type="info" size="small">
            {{ templateInfo }}
          </el-tag>
        </div>
        <div class="preview-actions">
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            type="success"
            :icon="Download"
            @click="handleExportPdf"
            :loading="exporting"
            size="small"
          >
            导出PDF
          </el-button>
          <el-button
            :icon="ZoomIn"
            @click="handleZoomIn"
            size="small"
            :disabled="scale >= maxScale"
          >
            放大
          </el-button>
          <el-button
            :icon="ZoomOut"
            @click="handleZoomOut"
            size="small"
            :disabled="scale <= minScale"
          >
            缩小
          </el-button>
          <el-button
            :icon="RefreshRight"
            @click="handleRefresh"
            size="small"
          >
            刷新
          </el-button>
        </div>
      </div>
    </template>

    <div class="preview-content" v-loading="loading">
      <div class="preview-container" :style="containerStyle">
        <div
          id="preview-render-container"
          class="preview-render-area"
          :style="renderAreaStyle"
        >
          <!-- 预览内容将在这里渲染 -->
        </div>
      </div>
    </div>

    <template #footer>
      <div class="preview-footer">
        <div class="scale-info">
          缩放比例: {{ Math.round(scale * 100) }}%
        </div>
        <div class="footer-actions">
          <el-button @click="visible = false">关闭</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import {
  Printer,
  Download,
  ZoomIn,
  ZoomOut,
  RefreshRight
} from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '打印预览'
  },
  templateInfo: {
    type: String,
    default: ''
  },
  width: {
    type: [String, Number],
    default: '80%'
  },
  height: {
    type: [String, Number],
    default: 'auto'
  }
});

const emit = defineEmits(['update:modelValue', 'print', 'export-pdf', 'refresh']);

// 响应式数据
const loading = ref(false);
const printing = ref(false);
const exporting = ref(false);
const scale = ref(1);
const minScale = 0.5;
const maxScale = 3;

// 当前预览的模板实例和数据
const currentTemplate = ref(null);
const currentData = ref(null);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const dialogWidth = computed(() => {
  if (typeof props.width === 'number') {
    return `${props.width}px`;
  }
  return props.width;
});

const dialogStyle = computed(() => {
  const style = {};
  if (props.height !== 'auto') {
    if (typeof props.height === 'number') {
      style.height = `${props.height}px`;
    } else {
      style.height = props.height;
    }
  }
  return style;
});

const containerStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: 'center top',
  transition: 'transform 0.3s ease'
}));

const renderAreaStyle = computed(() => ({
  minHeight: '400px',
  background: '#f5f5f5',
  padding: '20px',
  borderRadius: '4px',
  border: '1px solid #ddd'
}));

// 方法
const show = async (hiprintTemplate, printData = {}, options = {}) => {
  try {
    loading.value = true;
    currentTemplate.value = hiprintTemplate;
    currentData.value = printData;
    
    // 设置对话框尺寸
    if (options.width) {
      dialogWidth.value = options.width;
    }
    
    visible.value = true;
    
    await nextTick();
    await renderPreview();
  } catch (error) {
    console.error('显示预览失败:', error);
    EleMessage.error('显示预览失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

const renderPreview = async () => {
  try {
    const container = document.getElementById('preview-render-container');
    if (!container || !currentTemplate.value) {
      throw new Error('预览容器或模板不存在');
    }

    container.innerHTML = '';
    
    // 使用hiprint的getHtml方法获取预览内容
    if (typeof currentTemplate.value.getHtml === 'function') {
      const htmlContent = currentTemplate.value.getHtml(currentData.value);
      if (htmlContent && htmlContent.length > 0) {
        // 如果返回的是jQuery对象数组，取第一个元素的innerHTML
        if (htmlContent[0] && htmlContent[0].innerHTML) {
          container.innerHTML = htmlContent[0].innerHTML;
        } else if (typeof htmlContent === 'string') {
          container.innerHTML = htmlContent;
        } else {
          container.appendChild(htmlContent[0]);
        }
      } else {
        throw new Error('获取的HTML内容为空');
      }
    } else {
      throw new Error('模板不支持getHtml方法');
    }
  } catch (error) {
    console.error('渲染预览失败:', error);
    const container = document.getElementById('preview-render-container');
    if (container) {
      container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #999;">
          <p>预览渲染失败</p>
          <p style="font-size: 12px; margin-top: 8px;">${error.message}</p>
        </div>
      `;
    }
    throw error;
  }
};

const handlePrint = async () => {
  if (!currentTemplate.value) {
    EleMessage.error('没有可打印的模板');
    return;
  }

  printing.value = true;
  try {
    // 发射打印事件，让父组件处理具体的打印逻辑
    emit('print', {
      template: currentTemplate.value,
      data: currentData.value
    });
  } catch (error) {
    console.error('打印失败:', error);
    EleMessage.error('打印失败: ' + error.message);
  } finally {
    printing.value = false;
  }
};

const handleExportPdf = async () => {
  if (!currentTemplate.value) {
    EleMessage.error('没有可导出的模板');
    return;
  }

  exporting.value = true;
  try {
    // 发射导出PDF事件，让父组件处理具体的导出逻辑
    emit('export-pdf', {
      template: currentTemplate.value,
      data: currentData.value
    });
  } catch (error) {
    console.error('导出PDF失败:', error);
    EleMessage.error('导出PDF失败: ' + error.message);
  } finally {
    exporting.value = false;
  }
};

const handleZoomIn = () => {
  if (scale.value < maxScale) {
    scale.value = Math.min(scale.value + 0.1, maxScale);
  }
};

const handleZoomOut = () => {
  if (scale.value > minScale) {
    scale.value = Math.max(scale.value - 0.1, minScale);
  }
};

const handleRefresh = async () => {
  loading.value = true;
  try {
    await renderPreview();
    emit('refresh');
    EleMessage.success('预览已刷新');
  } catch (error) {
    EleMessage.error('刷新预览失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 监听visible变化，重置缩放比例
watch(visible, (newVal) => {
  if (newVal) {
    scale.value = 1;
  }
});

// 暴露方法给父组件
defineExpose({
  show,
  renderPreview
});
</script>

<style scoped>
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  max-height: 70vh;
  overflow: auto;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 400px;
  padding: 20px;
}

.preview-render-area {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scale-info {
  font-size: 12px;
  color: #909399;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

/* 预览对话框样式 */
:deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  max-height: 90vh;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: hidden;
  padding: 0;
}

:deep(.el-dialog__footer) {
  flex-shrink: 0;
}

/* hiprint预览内容样式 */
:deep(.hiprint-printPanel) {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  transform-origin: top center;
  max-width: 100%;
  height: auto;
}
</style>
