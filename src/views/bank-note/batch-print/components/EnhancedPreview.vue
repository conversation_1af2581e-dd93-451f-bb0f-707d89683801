<template>
  <!-- 增强的打印标签预览抽屉 -->
  <el-drawer
    v-model="visible"
    title=""
    direction="rtl"
    size="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="enhanced-preview-drawer"
  >
    <!-- 工具栏 -->
    <template #header>
      <div class="drawer-header">
        <div class="drawer-title-section">
          <span class="drawer-title">打印标签预览</span>
          <el-tag v-if="printData?.templateName" type="info" size="small">
            {{ printData.templateName }}
          </el-tag>
        </div>
        <div class="drawer-actions">
          <el-button-group>
            <el-button
              :icon="ZoomOut"
              @click="handleZoomOut"
              size="small"
              :disabled="scale <= minScale"
            />
            <el-button
              size="small"
              style="min-width: 60px;"
            >
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button
              :icon="ZoomIn"
              @click="handleZoomIn"
              size="small"
              :disabled="scale >= maxScale"
            />
          </el-button-group>
          <el-button
            :icon="RefreshRight"
            @click="handleRefresh"
            size="small"
          >
            刷新
          </el-button>
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownload"
            :loading="downloading"
            size="small"
          >
            下载
          </el-button>
        </div>
      </div>
    </template>

    <!-- 打印标签内容 -->
    <div class="print-content" v-loading="loading">
      <!-- 信息栏 -->
      <div class="print-info-bar">
        <el-alert
          :title="`共 ${printData?.totalCount || 0} 条记录待打印`"
          type="info"
          :closable="false"
          show-icon
        />
        <div class="info-details">
          <span>模板: {{ printData?.templateName || '系统默认' }}</span>
          <span>尺寸: {{ getCanvasSize() }}</span>
          <span>缩放: {{ Math.round(scale * 100) }}%</span>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="preview-area" :style="previewAreaStyle">
        <div class="print-page" :style="printPageStyle">
          <!-- 自定义模板渲染 -->
          <div class="custom-template-container">
            <div
              v-for="(item, index) in printData?.items || []"
              :key="item.id || index"
              class="custom-label-item"
              :style="getCustomLabelStyle()"
            >
              <div
                v-for="zone in getTemplateZones()"
                :key="zone.id"
                class="template-zone"
                :data-zone-id="zone.id"
                :style="getZoneStyle(zone)"
              >
                <!-- 特殊处理评级信息区域 -->
                <div v-if="zone.id === 'gradeInfo' && zone.multiFieldDisplay" class="grade-info-container">
                  <div
                    v-for="(displayConfig, index) in zone.multiFieldDisplay"
                    :key="`${displayConfig.field}-${index}`"
                    class="grade-display-item"
                    :class="`grade-${displayConfig.position}`"
                    :style="getGradeDisplayStyle(displayConfig)"
                    v-html="formatGradeDisplay(item[displayConfig.field], displayConfig)"
                  >
                  </div>
                </div>

                <!-- 普通字段显示 -->
                <div v-else>
                  <div
                    v-for="field in getZoneFields(zone.id, item)"
                    :key="field.name"
                    class="zone-field"
                    :style="getFieldStyle(zone, field)"
                  >
                    <!-- 二维码字段特殊处理 -->
                    <div v-if="isQRCodeField(field.name)" v-html="formatQRCodeDisplay(field, zone)"></div>
                    <!-- 普通字段 -->
                    <div v-else v-html="formatFieldDisplay(field, zone)"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import {
  Printer,
  Download,
  ZoomIn,
  ZoomOut,
  RefreshRight
} from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  printData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'confirm-print', 'refresh']);

// 响应式数据
const loading = ref(false);
const printing = ref(false);
const downloading = ref(false);
const scale = ref(1);
const minScale = 0.5;
const maxScale = 2;

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const previewAreaStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: 'center top',
  transition: 'transform 0.3s ease',
  padding: '20px'
}));

const printPageStyle = computed(() => ({
  background: 'white',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  padding: '20px',
  margin: '0 auto',
  maxWidth: '100%',
  overflow: 'hidden'
}));

// 方法
const getCanvasSize = () => {
  const canvas = props.printData?.layoutConfig?.canvas;
  if (canvas) {
    return `${canvas.width}mm × ${canvas.height}mm`;
  }
  return '200mm × 25mm';
};

const handleZoomIn = () => {
  if (scale.value < maxScale) {
    scale.value = Math.min(scale.value + 0.1, maxScale);
  }
};

const handleZoomOut = () => {
  if (scale.value > minScale) {
    scale.value = Math.max(scale.value - 0.1, minScale);
  }
};

const handleRefresh = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    emit('refresh');
    EleMessage.success('预览已刷新');
  }, 500);
};

const handlePrint = async () => {
  printing.value = true;
  try {
    // 创建新窗口进行打印，只打印标签内容
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintHTML();

    printWindow.document.write(printContent);
    printWindow.document.close();

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };

    emit('confirm-print', {
      ...props.printData,
      action: 'print'
    });
  } catch (error) {
    EleMessage.error('打印失败：' + error.message);
  } finally {
    printing.value = false;
  }
};

const handleDownload = async () => {
  downloading.value = true;
  try {
    emit('confirm-print', {
      ...props.printData,
      action: 'download'
    });
  } catch (error) {
    EleMessage.error('下载失败：' + error.message);
  } finally {
    downloading.value = false;
  }
};

// 生成打印HTML内容
const generatePrintHTML = () => {
  const labelItems = props.printData?.items || [];
  const zones = getTemplateZones();

  let labelsHTML = '';

  labelItems.forEach((item, index) => {
    labelsHTML += `
      <div class="print-label-item" style="${getPrintLabelStyle()}">
        ${zones.map(zone => `
          <div class="print-zone" style="${getPrintZoneStyle(zone)}">
            ${getZoneFields(zone.id, item).map(field => {
              if (isQRCodeField(field.name)) {
                return formatQRCodeDisplay(field, zone);
              } else {
                return `<div style="${getPrintFieldStyle(zone, field)}">${formatFieldDisplay(field, zone)}</div>`;
              }
            }).join('')}
          </div>
        `).join('')}
      </div>
    `;
  });

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>打印标签</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: white;
          padding: 10mm;
        }
        .print-label-item {
          page-break-inside: avoid;
          margin-bottom: 3mm;
        }
        .print-zone {
          position: absolute;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
          overflow: hidden;
        }
        .qr-code-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        @media print {
          body { padding: 0; }
          .print-label-item { margin-bottom: 2mm; }
        }
      </style>
    </head>
    <body>
      ${labelsHTML}
    </body>
    </html>
  `;
};

// 获取模板区域配置
const getTemplateZones = () => {
  if (!props.printData?.layoutConfig?.zones) {
    console.warn('模板区域配置缺失，使用默认配置');
    return getDefaultZones();
  }
  return props.printData.layoutConfig.zones;
};

// 默认区域配置
const getDefaultZones = () => {
  return [
    {
      id: 'logo',
      name: '公司Logo',
      x: 0, y: 0, width: 30, height: 25,
      fontSize: 12, color: '#333333',
      backgroundColor: '#f5f5f5',
      textAlign: 'center'
    },
    {
      id: 'coinInfo',
      name: '钱币信息',
      x: 30, y: 0, width: 120, height: 25,
      fontSize: 12, color: '#333333',
      backgroundColor: '#ffffff',
      textAlign: 'left'
    },
    {
      id: 'gradeInfo',
      name: '评级信息',
      x: 150, y: 0, width: 30, height: 25,
      fontSize: 12, color: '#333333',
      backgroundColor: '#fff3cd',
      textAlign: 'center'
    },
    {
      id: 'qrCode',
      name: '二维码',
      x: 180, y: 0, width: 20, height: 25,
      fontSize: 8, color: '#333333',
      backgroundColor: '#ffffff',
      textAlign: 'center'
    }
  ];
};

// 监听visible变化，重置缩放比例
watch(visible, (newVal) => {
  if (newVal) {
    scale.value = 1;
  }
});

// 获取自定义标签容器样式
const getCustomLabelStyle = () => {
  const canvas = props.printData?.layoutConfig?.canvas;
  return {
    position: 'relative',
    width: canvas?.width ? `${canvas.width}mm` : '200mm',
    height: canvas?.height ? `${canvas.height}mm` : '25mm',
    border: '2px solid #2c3e50',
    borderRadius: '4px',
    marginBottom: '12px',
    backgroundColor: '#ffffff',
    pageBreakInside: 'avoid',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
    boxSizing: 'border-box'
  };
};

// 获取区域样式
const getZoneStyle = (zone) => {
  const getZoneDefaults = (zoneId) => {
    switch (zoneId) {
      case 'logo':
        return { backgroundColor: '#f8f9fa', border: '1px solid #e9ecef', textAlign: 'center', fontWeight: 'bold' };
      case 'gradeInfo':
        return { backgroundColor: '#fff3cd', border: '1px solid #ffeaa7', textAlign: 'center', fontWeight: 'bold' };
      case 'qrCode':
        return { backgroundColor: '#ffffff', border: '1px solid #dee2e6', textAlign: 'center' };
      default:
        return { backgroundColor: 'transparent', textAlign: 'left' };
    }
  };

  const defaults = getZoneDefaults(zone.id);
  return {
    position: 'absolute',
    left: `${zone.x || 0}mm`,
    top: `${zone.y || 0}mm`,
    width: `${zone.width || 20}mm`,
    height: `${zone.height || 10}mm`,
    fontSize: `${zone.fontSize || 12}px`,
    color: zone.color || '#2c3e50',
    backgroundColor: zone.backgroundColor || defaults.backgroundColor,
    fontWeight: zone.fontWeight || defaults.fontWeight || 'normal',
    textAlign: zone.textAlign || defaults.textAlign || 'left',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: zone.verticalAlign || 'center',
    alignItems: zone.textAlign === 'center' ? 'center' : 'flex-start',
    padding: zone.padding ? `${zone.padding}px` : '4px',
    border: zone.showBorder ? `1px solid ${zone.borderColor || '#ccc'}` : (defaults.border || 'none'),
    borderRadius: zone.borderRadius ? `${zone.borderRadius}px` : '2px',
    overflow: 'hidden',
    lineHeight: zone.lineHeight || 1.3,
    boxSizing: 'border-box',
    textShadow: zone.id === 'gradeInfo' ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'
  };
};

// 获取区域字段数据
const getZoneFields = (zoneId, item) => {
  try {
    const fieldMapping = props.printData?.fieldMapping?.[zoneId] || [];

    if (!fieldMapping.length) {
      if (isSystemTemplate()) {
        return getDefaultZoneFields(zoneId, item);
      }
      return [];
    }

    const zone = props.printData?.layoutConfig?.zones?.find(z => z.id === zoneId);

    return fieldMapping.map(fieldName => {
      let fieldValue = '';

      if (item.customFields?.[zoneId]?.[fieldName]) {
        fieldValue = item.customFields[zoneId][fieldName];
      } else {
        fieldValue = getFieldValueFromItem(item, fieldName, zone);
      }

      return {
        name: fieldName,
        value: fieldValue || '',
        displayName: getFieldDisplayName(fieldName, zone)
      };
    });
  } catch (error) {
    console.error(`获取区域 ${zoneId} 字段数据失败:`, error);
    return [];
  }
};

// 检查是否为系统模板
const isSystemTemplate = () => {
  return !props.printData?.templateId || props.printData?.templateName === '系统默认模板';
};

// 获取默认区域字段数据
const getDefaultZoneFields = (zoneId, item) => {
  const defaultFields = {
    logo: [
      { name: 'companyLogo', value: 'CMG', displayName: '公司Logo' },
      { name: 'companyName', value: '中乾评级', displayName: '公司名称' }
    ],
    coinInfo: [
      { name: 'bankName', value: item.bankName || '', displayName: '发行银行' },
      { name: 'yearInfo', value: item.yearInfo || '', displayName: '年代信息' },
      { name: 'coinName', value: item.coinName || '', displayName: '钱币名称' },
      { name: 'serialNumber', value: item.serialNumber || '', displayName: '序列号' },
      { name: 'version', value: item.version || '', displayName: '版别' },
      { name: 'customerName', value: item.customerName || '', displayName: '客户姓名' }
    ],
    gradeInfo: [
      { name: 'gradeScore', value: item.gradeScore || '', displayName: '评级分数' },
      { name: 'gradeLevel', value: item.gradeLevel || '', displayName: '评级等级' },
      { name: 'specialMark', value: item.specialMark || '', displayName: '特殊标记' },
      { name: 'authenticity', value: item.authenticity || '', displayName: '真伪性' },
      { name: 'diyCode', value: item.diyCode || '', displayName: '送评条码' }
    ],
    qrCode: [
      { name: 'qrCode', value: item.diyCode || item.serialNumber || '', displayName: '二维码' },
      { name: 'qrCodeContent', value: item.diyCode || item.serialNumber || '', displayName: '二维码内容' }
    ]
  };

  return defaultFields[zoneId] || [];
};

// 暴露方法供外部调用
defineExpose({
  handleRefresh,
  handleZoomIn,
  handleZoomOut
});
</script>

<style scoped>
.enhanced-preview-drawer {
  /* 抽屉样式 */
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 16px;
}

.drawer-title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.drawer-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.print-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.print-info-bar {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.info-details {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.preview-area {
  flex: 1;
  overflow: auto;
  background: #f0f2f5;
  border-radius: 8px;
  min-height: 400px;
}

.print-page {
  min-height: 100%;
}

/* 其他样式继承自原来的print-labels.vue */
</style>
