<template>
  <!-- 增强的打印标签预览抽屉 -->
  <el-drawer
    v-model="visible"
    title=""
    direction="rtl"
    size="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="enhanced-preview-drawer"
  >
    <!-- 工具栏 -->
    <template #header>
      <div class="drawer-header">
        <div class="drawer-title-section">
          <span class="drawer-title">打印标签预览</span>
          <el-tag v-if="printData?.templateName" type="info" size="small">
            {{ printData.templateName }}
          </el-tag>
        </div>
        <div class="drawer-actions">
          <el-button-group>
            <el-button
              :icon="ZoomOut"
              @click="handleZoomOut"
              size="small"
              :disabled="scale <= minScale"
            />
            <el-button
              size="small"
              style="min-width: 60px;"
            >
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button
              :icon="ZoomIn"
              @click="handleZoomIn"
              size="small"
              :disabled="scale >= maxScale"
            />
          </el-button-group>
          <el-button
            :icon="RefreshRight"
            @click="handleRefresh"
            size="small"
          >
            刷新
          </el-button>
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownload"
            :loading="downloading"
            size="small"
          >
            下载
          </el-button>
        </div>
      </div>
    </template>

    <!-- 打印标签内容 -->
    <div class="print-content" v-loading="loading">
      <!-- 信息栏 -->
      <div class="print-info-bar">
        <el-alert
          :title="`共 ${printData?.totalCount || 0} 条记录待打印`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 预览区域 -->
      <div class="preview-area" :style="previewAreaStyle">
        <div class="print-page" :style="printPageStyle">
          <!-- hiprint模板渲染容器 -->
          <div
            id="hiprint-preview-container"
            class="hiprint-preview-container"
            v-loading="renderLoading"
          >
            <!-- hiprint渲染的HTML内容将在这里显示 -->
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import {
  Printer,
  Download,
  ZoomIn,
  ZoomOut,
  RefreshRight
} from '@element-plus/icons-vue';

// 动态导入hiprint
let hiprint = null;
let hiprintTemplate = null;

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  printData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'confirm-print', 'refresh']);

// 响应式数据
const loading = ref(false);
const renderLoading = ref(false);
const printing = ref(false);
const downloading = ref(false);
const scale = ref(1);
const minScale = 0.5;
const maxScale = 2;

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const previewAreaStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: 'center top',
  transition: 'transform 0.3s ease',
  padding: '20px'
}));

const printPageStyle = computed(() => ({
  background: 'white',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  padding: '20px',
  margin: '0 auto',
  maxWidth: '100%',
  overflow: 'hidden'
}));

// 初始化hiprint
const initHiprint = async () => {
  try {
    if (window.hiprint) {
      hiprint = window.hiprint;
      return;
    }

    // 动态加载hiprint
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/vue-plugin-hiprint@latest/dist/vue-plugin-hiprint.js';

    return new Promise((resolve, reject) => {
      script.onload = () => {
        if (window['vue-plugin-hiprint']) {
          hiprint = window['vue-plugin-hiprint'].hiprint;
          const defaultElementTypeProvider = window['vue-plugin-hiprint'].defaultElementTypeProvider;

          // 初始化hiprint
          hiprint.init({
            providers: [new defaultElementTypeProvider()]
          });

          resolve();
        } else {
          reject(new Error('hiprint加载失败'));
        }
      };

      script.onerror = () => {
        reject(new Error('hiprint脚本加载失败'));
      };

      document.head.appendChild(script);
    });
  } catch (error) {
    console.error('初始化hiprint失败:', error);
    throw error;
  }
};

// 渲染预览
const renderPreview = async () => {
  if (!props.printData?.processedTemplates?.[0]?.panels?.[0]) {
    throw new Error('没有可预览的模板数据');
  }

  renderLoading.value = true;

  try {
    // 确保hiprint已初始化
    await initHiprint();

    // 获取处理后的模板数据
    const templateData = {
      panels: props.printData.processedTemplates[0].panels
    };

    // 创建hiprint模板实例
    hiprintTemplate = new hiprint.PrintTemplate({
      template: templateData
    });

    // 获取HTML内容并渲染
    await nextTick();
    const container = document.getElementById('hiprint-preview-container');
    if (container && hiprintTemplate) {
      // 使用getHtml方法获取渲染后的HTML
      const htmlContent = hiprintTemplate.getHtml();
      if (htmlContent && htmlContent.length > 0) {
        // 清空容器并添加新内容
        container.innerHTML = '';
        if (typeof htmlContent === 'string') {
          container.innerHTML = htmlContent;
        } else if (htmlContent[0]) {
          // 如果是jQuery对象或DOM元素数组
          if (htmlContent[0].innerHTML) {
            container.innerHTML = htmlContent[0].innerHTML;
          } else {
            container.appendChild(htmlContent[0]);
          }
        }
      } else {
        throw new Error('获取的HTML内容为空');
      }
    } else {
      throw new Error('预览容器不存在');
    }
  } catch (error) {
    console.error('渲染预览失败:', error);
    const container = document.getElementById('hiprint-preview-container');
    if (container) {
      container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #999;">
          <p>预览渲染失败</p>
          <p style="font-size: 12px; margin-top: 8px;">${error.message}</p>
        </div>
      `;
    }
    throw error;
  } finally {
    renderLoading.value = false;
  }
};

const handleZoomIn = () => {
  if (scale.value < maxScale) {
    scale.value = Math.min(scale.value + 0.1, maxScale);
  }
};

const handleZoomOut = () => {
  if (scale.value > minScale) {
    scale.value = Math.max(scale.value - 0.1, minScale);
  }
};

const handleRefresh = async () => {
  loading.value = true;
  try {
    await renderPreview();
    emit('refresh');
    EleMessage.success('预览已刷新');
  } catch (error) {
    EleMessage.error('刷新预览失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

const handlePrint = async () => {
  if (!hiprintTemplate) {
    EleMessage.error('模板未初始化，请先刷新预览');
    return;
  }

  printing.value = true;
  try {
    // 使用hiprint的打印功能
    if (typeof hiprintTemplate.print === 'function') {
      hiprintTemplate.print({}, {}, {
        callback: () => {
          console.log('打印完成');
          EleMessage.success('打印任务已发送');
        }
      });
    } else {
      // 备选方案：打开新窗口进行打印
      const printWindow = window.open('', '_blank');
      const htmlContent = hiprintTemplate.getHtml();

      const printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>打印预览</title>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            @media print { body { padding: 0; } }
          </style>
        </head>
        <body>
          ${htmlContent[0].innerHTML}
        </body>
        </html>
      `;

      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    }

    emit('confirm-print', {
      ...props.printData,
      action: 'print'
    });
  } catch (error) {
    EleMessage.error('打印失败：' + error.message);
  } finally {
    printing.value = false;
  }
};

const handleDownload = async () => {
  if (!hiprintTemplate) {
    EleMessage.error('模板未初始化，请先刷新预览');
    return;
  }

  downloading.value = true;
  try {
    // 使用hiprint的PDF导出功能
    if (typeof hiprintTemplate.toPdf === 'function') {
      const fileName = `${props.printData?.templateName || '标签打印'}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.pdf`;
      await hiprintTemplate.toPdf({}, fileName, { isDownload: true });
      EleMessage.success('PDF导出成功');
    } else {
      throw new Error('模板不支持PDF导出功能');
    }

    emit('confirm-print', {
      ...props.printData,
      action: 'download'
    });
  } catch (error) {
    EleMessage.error('下载失败：' + error.message);
  } finally {
    downloading.value = false;
  }
};

// 获取模板区域配置
const getTemplateZones = () => {
  if (!props.printData?.layoutConfig?.zones) {
    console.warn('模板区域配置缺失，使用默认配置');
    return getDefaultZones();
  }
  return props.printData.layoutConfig.zones;
};

// 默认区域配置
const getDefaultZones = () => {
  return [
    {
      id: 'logo',
      name: '公司Logo',
      x: 0, y: 0, width: 30, height: 25,
      fontSize: 12, color: '#333333',
      backgroundColor: '#f5f5f5',
      textAlign: 'center'
    },
    {
      id: 'coinInfo',
      name: '钱币信息',
      x: 30, y: 0, width: 120, height: 25,
      fontSize: 12, color: '#333333',
      backgroundColor: '#ffffff',
      textAlign: 'left'
    },
    {
      id: 'gradeInfo',
      name: '评级信息',
      x: 150, y: 0, width: 30, height: 25,
      fontSize: 12, color: '#333333',
      backgroundColor: '#fff3cd',
      textAlign: 'center'
    },
    {
      id: 'qrCode',
      name: '二维码',
      x: 180, y: 0, width: 20, height: 25,
      fontSize: 8, color: '#333333',
      backgroundColor: '#ffffff',
      textAlign: 'center'
    }
  ];
};

// 监听visible变化，重置缩放比例并渲染预览
watch(visible, async (newVal) => {
  if (newVal) {
    scale.value = 1;
    // 延迟一点时间确保DOM渲染完成
    await nextTick();
    setTimeout(async () => {
      try {
        await renderPreview();
      } catch (error) {
        console.error('自动渲染预览失败:', error);
      }
    }, 100);
  }
});

// 暴露方法供外部调用
defineExpose({
  handleRefresh,
  handleZoomIn,
  handleZoomOut
});
</script>

<style scoped>
.enhanced-preview-drawer {
  /* 抽屉样式 */
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 16px;
}

.drawer-title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.drawer-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.print-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.print-info-bar {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.info-details {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.preview-area {
  flex: 1;
  overflow: auto;
  background: #f0f2f5;
  border-radius: 8px;
  min-height: 400px;
}

.print-page {
  min-height: 100%;
}

.hiprint-preview-container {
  min-height: 400px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
}

/* hiprint生成的内容样式 */
.hiprint-preview-container :deep(.hiprint-printPanel) {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  transform-origin: top center;
  max-width: 100%;
  height: auto;
}

.hiprint-preview-container :deep(.hiprint-printElement) {
  box-sizing: border-box;
}

.hiprint-preview-container :deep(.hiprint-printElement-text) {
  word-break: break-word;
  overflow-wrap: break-word;
}

.hiprint-preview-container :deep(.hiprint-printElement-qrcode) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hiprint-preview-container :deep(.hiprint-printElement-qrcode img) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
</style>
